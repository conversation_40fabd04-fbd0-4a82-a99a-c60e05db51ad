#!/usr/bin/env python3
"""
Simple Website Crawler
Crawl website về thành một file HTML đơn giản
"""

import requests
import re
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import base64
import sys

def download_and_embed_assets(url):
    """Download website và embed tất cả assets"""
    print(f"🚀 Downloading: {url}")
    
    # Download main page
    response = requests.get(url, headers={
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    response.raise_for_status()
    
    soup = BeautifulSoup(response.text, 'html.parser')
    base_url = url.rstrip('/')
    
    print("📦 Processing assets...")
    
    # Process CSS files
    for link in soup.find_all('link', rel='stylesheet'):
        href = link.get('href')
        if href:
            try:
                css_url = urljoin(base_url, href)
                print(f"  📄 CSS: {css_url}")
                css_response = requests.get(css_url, timeout=10)
                css_response.raise_for_status()
                
                # Create style tag
                style_tag = soup.new_tag('style', type='text/css')
                style_tag.string = f"\n/* From: {css_url} */\n{css_response.text}\n"
                
                # Replace link with style
                link.replace_with(style_tag)
            except Exception as e:
                print(f"    ❌ Failed: {e}")
    
    # Process JavaScript files
    for script in soup.find_all('script', src=True):
        src = script.get('src')
        if src:
            try:
                js_url = urljoin(base_url, src)
                print(f"  📜 JS: {js_url}")
                js_response = requests.get(js_url, timeout=10)
                js_response.raise_for_status()
                
                # Replace src with inline content
                script['src'] = None
                script.string = f"\n/* From: {js_url} */\n{js_response.text}\n"
            except Exception as e:
                print(f"    ❌ Failed: {e}")
    
    # Process images
    for img in soup.find_all('img', src=True):
        src = img.get('src')
        if src and not src.startswith('data:'):
            try:
                img_url = urljoin(base_url, src)
                print(f"  🖼️  IMG: {img_url}")
                img_response = requests.get(img_url, timeout=10)
                img_response.raise_for_status()
                
                # Convert to base64
                content_type = img_response.headers.get('content-type', 'image/jpeg')
                base64_data = base64.b64encode(img_response.content).decode('utf-8')
                data_uri = f"data:{content_type};base64,{base64_data}"
                
                img['src'] = data_uri
            except Exception as e:
                print(f"    ❌ Failed: {e}")
    
    # Process background images in style attributes
    for element in soup.find_all(style=True):
        style_attr = element.get('style', '')
        if 'background-image' in style_attr or 'background:' in style_attr:
            # Find URLs in style
            url_pattern = r'url\(["\']?([^"\')\s]+)["\']?\)'
            matches = re.findall(url_pattern, style_attr)
            
            for match in matches:
                if not match.startswith('data:'):
                    try:
                        bg_url = urljoin(base_url, match)
                        print(f"  🎨 BG: {bg_url}")
                        bg_response = requests.get(bg_url, timeout=10)
                        bg_response.raise_for_status()
                        
                        content_type = bg_response.headers.get('content-type', 'image/jpeg')
                        base64_data = base64.b64encode(bg_response.content).decode('utf-8')
                        data_uri = f"data:{content_type};base64,{base64_data}"
                        
                        style_attr = style_attr.replace(f'url({match})', f'url({data_uri})')
                        style_attr = style_attr.replace(f'url("{match}")', f'url("{data_uri}")')
                        style_attr = style_attr.replace(f"url('{match}')", f'url("{data_uri}")')
                        
                        element['style'] = style_attr
                    except Exception as e:
                        print(f"    ❌ Failed: {e}")
    
    # Add meta tags
    head = soup.find('head')
    if head:
        # Add charset if not exists
        if not soup.find('meta', charset=True):
            charset_meta = soup.new_tag('meta', charset='UTF-8')
            head.insert(0, charset_meta)
        
        # Add viewport if not exists
        if not soup.find('meta', attrs={'name': 'viewport'}):
            viewport_meta = soup.new_tag('meta', attrs={
                'name': 'viewport',
                'content': 'width=device-width, initial-scale=1.0'
            })
            head.append(viewport_meta)
        
        # Add generator meta
        generator_meta = soup.new_tag('meta', attrs={
            'name': 'generator',
            'content': 'Simple Website Crawler'
        })
        head.append(generator_meta)
    
    return str(soup)

def main():
    if len(sys.argv) != 2:
        print("Usage: python simple_crawler.py <URL>")
        print("Example: python simple_crawler.py https://vandamtour.vn")
        sys.exit(1)
    
    url = sys.argv[1]
    output_file = "simple_index.html"
    
    try:
        html_content = download_and_embed_assets(url)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        # Get file size
        import os
        file_size = os.path.getsize(output_file)
        file_size_mb = file_size / (1024 * 1024)
        
        print("\n" + "="*50)
        print("🎉 CRAWL COMPLETED!")
        print("="*50)
        print(f"📁 Output: {output_file}")
        print(f"📊 Size: {file_size_mb:.2f} MB")
        print(f"🌐 Open: file://{os.path.abspath(output_file)}")
        print("="*50)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
