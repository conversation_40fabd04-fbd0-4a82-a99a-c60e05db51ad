#!/usr/bin/env python3
"""
Extract Assets - Tách CSS và JS từ HTML thành các file riêng biệt
Tách file index.html thành 3 file: HTML, CSS, JS
"""

import os
import re
from bs4 import BeautifulSoup
from pathlib import Path

class AssetExtractor:
    """
    Tách CSS và JS từ HTML thành các file riêng biệt
    """
    
    def __init__(self, html_file: str):
        self.html_file = html_file
        self.base_name = Path(html_file).stem
        self.css_file = f"{self.base_name}.css"
        self.js_file = f"{self.base_name}.js"
        
        print(f"🚀 Asset Extractor initialized")
        print(f"📄 Input: {html_file}")
        print(f"📄 Output HTML: {html_file}")
        print(f"🎨 Output CSS: {self.css_file}")
        print(f"📜 Output JS: {self.js_file}")
    
    def extract_assets(self):
        """Tách CSS và JS từ HTML"""
        try:
            # Read HTML file
            with open(self.html_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Extract CSS
            css_content = self.extract_css(soup)
            
            # Extract JS
            js_content = self.extract_js(soup)
            
            # Write CSS file
            if css_content:
                with open(self.css_file, 'w', encoding='utf-8') as f:
                    f.write(css_content)
                print(f"✅ CSS extracted: {self.css_file} ({len(css_content)} chars)")
            
            # Write JS file
            if js_content:
                with open(self.js_file, 'w', encoding='utf-8') as f:
                    f.write(js_content)
                print(f"✅ JS extracted: {self.js_file} ({len(js_content)} chars)")
            
            # Add links to external files
            self.add_external_links(soup)
            
            # Write updated HTML
            with open(self.html_file, 'w', encoding='utf-8') as f:
                f.write(str(soup))
            
            print(f"✅ HTML updated: {self.html_file}")
            
            # Print summary
            self.print_summary()
            
            return True
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
    
    def extract_css(self, soup):
        """Extract tất cả CSS từ HTML"""
        css_parts = []
        
        # Extract from <style> tags
        style_tags = soup.find_all('style')
        for i, style in enumerate(style_tags):
            if style.string:
                css_parts.append(f"/* ===== STYLE BLOCK {i+1} ===== */")
                css_parts.append(style.string.strip())
                css_parts.append("")
                
                # Remove style tag
                style.decompose()
        
        # Extract from style attributes
        elements_with_style = soup.find_all(style=True)
        if elements_with_style:
            inline_css = []
            for j, element in enumerate(elements_with_style):
                style_attr = element.get('style', '').strip()
                if style_attr:
                    # Create CSS rule for this element
                    element_id = f"inline-style-{j+1}"
                    element['id'] = element_id
                    
                    css_rule = f"#{element_id} {{ {style_attr} }}"
                    inline_css.append(css_rule)
                    
                    # Remove style attribute
                    del element['style']
            
            if inline_css:
                css_parts.append("/* ===== INLINE STYLES CONVERTED ===== */")
                css_parts.extend(inline_css)
                css_parts.append("")
        
        return "\n".join(css_parts)
    
    def extract_js(self, soup):
        """Extract tất cả JavaScript từ HTML"""
        js_parts = []
        
        # Extract from <script> tags (without src)
        script_tags = soup.find_all('script')
        for i, script in enumerate(script_tags):
            # Skip scripts with src attribute
            if script.get('src'):
                continue
                
            if script.string:
                js_parts.append(f"/* ===== SCRIPT BLOCK {i+1} ===== */")
                js_parts.append(script.string.strip())
                js_parts.append("")
                
                # Remove script tag
                script.decompose()
        
        # Extract from event attributes (onclick, onload, etc.)
        event_attrs = ['onclick', 'onload', 'onmouseover', 'onmouseout', 'onchange', 'onsubmit', 'onfocus', 'onblur']
        inline_js = []
        
        for attr in event_attrs:
            elements_with_event = soup.find_all(**{attr: True})
            for j, element in enumerate(elements_with_event):
                event_code = element.get(attr, '').strip()
                if event_code:
                    # Create function for this event
                    element_id = f"event-{attr}-{j+1}"
                    if not element.get('id'):
                        element['id'] = element_id
                    else:
                        element_id = element['id']
                    
                    function_name = f"handle_{attr}_{j+1}"
                    
                    js_function = f"""
function {function_name}() {{
    {event_code}
}}

// Attach event listener
document.addEventListener('DOMContentLoaded', function() {{
    var element = document.getElementById('{element_id}');
    if (element) {{
        element.addEventListener('{attr[2:]}', {function_name});
    }}
}});"""
                    
                    inline_js.append(js_function)
                    
                    # Remove event attribute
                    del element[attr]
        
        if inline_js:
            js_parts.append("/* ===== INLINE EVENT HANDLERS CONVERTED ===== */")
            js_parts.extend(inline_js)
            js_parts.append("")
        
        return "\n".join(js_parts)
    
    def add_external_links(self, soup):
        """Add links to external CSS and JS files"""
        head = soup.find('head')
        if not head:
            head = soup.new_tag('head')
            if soup.html:
                soup.html.insert(0, head)
            else:
                soup.insert(0, head)
        
        # Add CSS link
        if os.path.exists(self.css_file):
            css_link = soup.new_tag('link', rel='stylesheet', href=self.css_file)
            head.append(css_link)
        
        # Add JS script tag before closing body
        if os.path.exists(self.js_file):
            body = soup.find('body')
            if body:
                js_script = soup.new_tag('script', src=self.js_file)
                body.append(js_script)
            else:
                # If no body, add to head
                js_script = soup.new_tag('script', src=self.js_file)
                head.append(js_script)
    
    def print_summary(self):
        """In tóm tắt kết quả"""
        print("\n" + "="*60)
        print("🎉 ASSET EXTRACTION COMPLETED!")
        print("="*60)
        
        # File sizes
        html_size = os.path.getsize(self.html_file) if os.path.exists(self.html_file) else 0
        css_size = os.path.getsize(self.css_file) if os.path.exists(self.css_file) else 0
        js_size = os.path.getsize(self.js_file) if os.path.exists(self.js_file) else 0
        
        total_size = html_size + css_size + js_size
        
        print(f"📁 Files created:")
        print(f"   📄 {self.html_file}: {html_size/1024:.1f} KB")
        if css_size > 0:
            print(f"   🎨 {self.css_file}: {css_size/1024:.1f} KB")
        if js_size > 0:
            print(f"   📜 {self.js_file}: {js_size/1024:.1f} KB")
        
        print(f"\n📊 Total size: {total_size/1024:.1f} KB")
        
        print(f"\n🌐 Usage:")
        print(f"   Open: {os.path.abspath(self.html_file)}")
        print(f"   CSS: {os.path.abspath(self.css_file)}")
        print(f"   JS: {os.path.abspath(self.js_file)}")
        
        print("\n✨ Benefits:")
        print("   - Separated concerns (HTML/CSS/JS)")
        print("   - Easier to maintain and edit")
        print("   - Better caching in browsers")
        print("   - Cleaner HTML structure")
        
        print("\n" + "="*60)

def main():
    """Main function"""
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python extract_assets.py <HTML_FILE>")
        print("Example: python extract_assets.py index.html")
        sys.exit(1)
    
    html_file = sys.argv[1]
    
    if not os.path.exists(html_file):
        print(f"❌ File not found: {html_file}")
        sys.exit(1)
    
    # Create extractor and run
    extractor = AssetExtractor(html_file)
    success = extractor.extract_assets()
    
    if success:
        print("\n🎉 Asset extraction successful!")
    else:
        print("\n❌ Asset extraction failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
