#!/usr/bin/env python3
"""
Website Crawler - VẠN DẶM TOUR
Crawl toàn bộ website về thành một trang index.html duy nhất

Author: AI Assistant
Version: 1.0.0
Date: 2025-07-30
"""

import requests
import os
import re
import time
from urllib.parse import urljoin, urlparse, parse_qs
from bs4 import BeautifulSoup
import base64
import mimetypes
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WebsiteCrawler:
    """
    Crawl toàn bộ website về thành một file HTML duy nhất
    """
    
    def __init__(self, base_url: str, output_file: str = "index.html"):
        self.base_url = base_url.rstrip('/')
        self.domain = urlparse(base_url).netloc
        self.output_file = output_file
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # Storage for assets
        self.css_content = []
        self.js_content = []
        self.images = {}
        self.fonts = {}
        
        # Processed URLs to avoid duplicates
        self.processed_urls = set()
        
        logger.info(f"Website Crawler initialized for: {self.base_url}")
    
    def download_asset(self, url: str, asset_type: str = 'image') -> str:
        """Download asset và convert thành base64"""
        try:
            if url in self.processed_urls:
                return self.get_cached_asset(url, asset_type)
            
            # Resolve relative URLs
            if url.startswith('//'):
                url = 'https:' + url
            elif url.startswith('/'):
                url = self.base_url + url
            elif not url.startswith('http'):
                url = urljoin(self.base_url, url)
            
            logger.info(f"Downloading {asset_type}: {url}")
            
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            # Get MIME type
            content_type = response.headers.get('content-type', '')
            if not content_type:
                content_type = mimetypes.guess_type(url)[0] or 'application/octet-stream'
            
            # Convert to base64
            base64_data = base64.b64encode(response.content).decode('utf-8')
            data_uri = f"data:{content_type};base64,{base64_data}"
            
            # Cache the asset
            if asset_type == 'image':
                self.images[url] = data_uri
            elif asset_type == 'font':
                self.fonts[url] = data_uri
            
            self.processed_urls.add(url)
            
            logger.info(f"✓ Downloaded {asset_type}: {url} ({len(response.content)} bytes)")
            return data_uri
            
        except Exception as e:
            logger.error(f"✗ Failed to download {asset_type} {url}: {e}")
            return url  # Return original URL as fallback
    
    def get_cached_asset(self, url: str, asset_type: str) -> str:
        """Get cached asset"""
        if asset_type == 'image' and url in self.images:
            return self.images[url]
        elif asset_type == 'font' and url in self.fonts:
            return self.fonts[url]
        return url
    
    def download_css(self, url: str) -> str:
        """Download CSS và process các assets bên trong"""
        try:
            if url.startswith('//'):
                url = 'https:' + url
            elif url.startswith('/'):
                url = self.base_url + url
            elif not url.startswith('http'):
                url = urljoin(self.base_url, url)
            
            logger.info(f"Downloading CSS: {url}")
            
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            css_content = response.text
            
            # Process URLs in CSS (images, fonts, etc.)
            url_pattern = r'url\(["\']?([^"\')\s]+)["\']?\)'
            
            def replace_url(match):
                asset_url = match.group(1)
                
                # Skip data URIs
                if asset_url.startswith('data:'):
                    return match.group(0)
                
                # Resolve relative URLs
                if asset_url.startswith('//'):
                    full_url = 'https:' + asset_url
                elif asset_url.startswith('/'):
                    full_url = self.base_url + asset_url
                elif not asset_url.startswith('http'):
                    full_url = urljoin(url, asset_url)
                else:
                    full_url = asset_url
                
                # Determine asset type
                asset_type = 'font' if any(ext in asset_url.lower() for ext in ['.woff', '.woff2', '.ttf', '.eot']) else 'image'
                
                # Download and convert to base64
                data_uri = self.download_asset(full_url, asset_type)
                
                return f'url("{data_uri}")'
            
            # Replace all URLs in CSS
            processed_css = re.sub(url_pattern, replace_url, css_content)
            
            logger.info(f"✓ Processed CSS: {url}")
            return processed_css
            
        except Exception as e:
            logger.error(f"✗ Failed to download CSS {url}: {e}")
            return f"/* Failed to load CSS from {url}: {e} */"
    
    def download_js(self, url: str) -> str:
        """Download JavaScript"""
        try:
            if url.startswith('//'):
                url = 'https:' + url
            elif url.startswith('/'):
                url = self.base_url + url
            elif not url.startswith('http'):
                url = urljoin(self.base_url, url)
            
            logger.info(f"Downloading JS: {url}")
            
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            logger.info(f"✓ Downloaded JS: {url}")
            return response.text
            
        except Exception as e:
            logger.error(f"✗ Failed to download JS {url}: {e}")
            return f"/* Failed to load JS from {url}: {e} */"
    
    def process_html(self, html_content: str) -> str:
        """Process HTML và download tất cả assets"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        logger.info("Processing HTML content...")
        
        # Process CSS links
        for link in soup.find_all('link', rel='stylesheet'):
            href = link.get('href')
            if href:
                css_content = self.download_css(href)
                self.css_content.append(css_content)
                link.decompose()  # Remove original link
        
        # Process inline styles
        for style in soup.find_all('style'):
            if style.string:
                # Process URLs in inline CSS
                css_content = style.string
                url_pattern = r'url\(["\']?([^"\')\s]+)["\']?\)'
                
                def replace_url(match):
                    asset_url = match.group(1)
                    if asset_url.startswith('data:'):
                        return match.group(0)
                    
                    if asset_url.startswith('//'):
                        full_url = 'https:' + asset_url
                    elif asset_url.startswith('/'):
                        full_url = self.base_url + asset_url
                    elif not asset_url.startswith('http'):
                        full_url = urljoin(self.base_url, asset_url)
                    else:
                        full_url = asset_url
                    
                    asset_type = 'font' if any(ext in asset_url.lower() for ext in ['.woff', '.woff2', '.ttf', '.eot']) else 'image'
                    data_uri = self.download_asset(full_url, asset_type)
                    return f'url("{data_uri}")'
                
                processed_css = re.sub(url_pattern, replace_url, css_content)
                style.string = processed_css
        
        # Process JavaScript
        for script in soup.find_all('script', src=True):
            src = script.get('src')
            if src:
                js_content = self.download_js(src)
                self.js_content.append(js_content)
                script['src'] = None  # Remove src attribute
                script.string = js_content
        
        # Process images
        for img in soup.find_all('img', src=True):
            src = img.get('src')
            if src and not src.startswith('data:'):
                data_uri = self.download_asset(src, 'image')
                img['src'] = data_uri
        
        # Process background images in style attributes
        for element in soup.find_all(style=True):
            style_attr = element.get('style', '')
            if 'background-image' in style_attr or 'background:' in style_attr:
                url_pattern = r'url\(["\']?([^"\')\s]+)["\']?\)'
                
                def replace_bg_url(match):
                    asset_url = match.group(1)
                    if asset_url.startswith('data:'):
                        return match.group(0)
                    
                    if asset_url.startswith('//'):
                        full_url = 'https:' + asset_url
                    elif asset_url.startswith('/'):
                        full_url = self.base_url + asset_url
                    elif not asset_url.startswith('http'):
                        full_url = urljoin(self.base_url, asset_url)
                    else:
                        full_url = asset_url
                    
                    data_uri = self.download_asset(full_url, 'image')
                    return f'url("{data_uri}")'
                
                processed_style = re.sub(url_pattern, replace_bg_url, style_attr)
                element['style'] = processed_style
        
        # Process favicons and other icons
        for link in soup.find_all('link', href=True):
            rel = link.get('rel', [])
            if isinstance(rel, str):
                rel = [rel]
            
            if any(r in ['icon', 'shortcut icon', 'apple-touch-icon'] for r in rel):
                href = link.get('href')
                if href and not href.startswith('data:'):
                    data_uri = self.download_asset(href, 'image')
                    link['href'] = data_uri
        
        logger.info("✓ HTML processing completed")
        return str(soup)
    
    def create_single_page(self) -> str:
        """Tạo single page HTML với tất cả assets embedded"""
        logger.info("Creating single page HTML...")
        
        # Download main page
        logger.info(f"Downloading main page: {self.base_url}")
        response = self.session.get(self.base_url, timeout=30)
        response.raise_for_status()
        
        # Process HTML
        processed_html = self.process_html(response.text)
        soup = BeautifulSoup(processed_html, 'html.parser')
        
        # Add combined CSS to head
        if self.css_content:
            head = soup.find('head')
            if not head:
                head = soup.new_tag('head')
                soup.html.insert(0, head)
            
            combined_css = '\n\n'.join(self.css_content)
            style_tag = soup.new_tag('style', type='text/css')
            style_tag.string = f"\n/* Combined CSS from all stylesheets */\n{combined_css}\n"
            head.append(style_tag)
        
        # Add combined JS before closing body
        if self.js_content:
            body = soup.find('body')
            if body:
                combined_js = '\n\n'.join(self.js_content)
                script_tag = soup.new_tag('script', type='text/javascript')
                script_tag.string = f"\n/* Combined JavaScript from all scripts */\n{combined_js}\n"
                body.append(script_tag)
        
        # Add meta tags for better compatibility
        head = soup.find('head')
        if head:
            # Add charset if not exists
            if not soup.find('meta', charset=True):
                charset_meta = soup.new_tag('meta', charset='UTF-8')
                head.insert(0, charset_meta)
            
            # Add viewport if not exists
            if not soup.find('meta', attrs={'name': 'viewport'}):
                viewport_meta = soup.new_tag('meta', attrs={
                    'name': 'viewport',
                    'content': 'width=device-width, initial-scale=1.0'
                })
                head.append(viewport_meta)
            
            # Add generator meta
            generator_meta = soup.new_tag('meta', attrs={
                'name': 'generator',
                'content': 'Website Crawler v1.0.0'
            })
            head.append(generator_meta)
        
        logger.info("✓ Single page HTML created")
        return str(soup)
    
    def crawl_website(self) -> bool:
        """Crawl toàn bộ website"""
        try:
            logger.info("🚀 Starting website crawl...")
            start_time = time.time()
            
            # Create single page
            single_page_html = self.create_single_page()
            
            # Write to file
            with open(self.output_file, 'w', encoding='utf-8') as f:
                f.write(single_page_html)
            
            # Summary
            end_time = time.time()
            duration = end_time - start_time
            
            file_size = os.path.getsize(self.output_file)
            file_size_mb = file_size / (1024 * 1024)
            
            self.print_summary(duration, file_size_mb)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Crawl failed: {e}")
            return False
    
    def print_summary(self, duration: float, file_size_mb: float):
        """In tóm tắt kết quả"""
        print("\n" + "="*60)
        print("🎉 WEBSITE CRAWL COMPLETED!")
        print("="*60)
        
        print(f"🎯 Source URL: {self.base_url}")
        print(f"📁 Output file: {self.output_file}")
        print(f"⏱️  Duration: {duration:.2f} seconds")
        print(f"📊 File size: {file_size_mb:.2f} MB")
        
        print(f"\n📈 Assets processed:")
        print(f"   - CSS files: {len(self.css_content)}")
        print(f"   - JS files: {len(self.js_content)}")
        print(f"   - Images: {len(self.images)}")
        print(f"   - Fonts: {len(self.fonts)}")
        print(f"   - Total URLs: {len(self.processed_urls)}")
        
        print(f"\n🌐 Usage:")
        print(f"   Open: {os.path.abspath(self.output_file)}")
        print(f"   Works offline with all assets embedded!")
        
        print("\n" + "="*60)

def main():
    """Main function"""
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python website_crawler.py <URL>")
        print("Example: python website_crawler.py https://vandamtour.vn")
        sys.exit(1)
    
    url = sys.argv[1]
    
    # Create crawler and run
    crawler = WebsiteCrawler(url)
    success = crawler.crawl_website()
    
    if success:
        print("\n🎉 Website crawl successful!")
    else:
        print("\n❌ Website crawl failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
