#!/usr/bin/env python3
"""
Professional WordPress to HTML Converter
Sử dụng các repo chuyên nghiệp có sẵn để convert WordPress thành HTML/CSS/JS có cấu trúc

Dựa trên các repo chuyên nghiệp:
1. PyWebCopy - https://github.com/rajatomar788/pywebcopy
2. Simply Static (WordPress Plugin) - https://github.com/Simply-Static/simply-static
3. HTTrack integration
4. Custom structured output

Author: AI Assistant
Version: 1.0.0
Date: 2025-07-30
"""

import os
import sys
import subprocess
import time
import json
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ProfessionalWPConverter:
    """
    Professional WordPress to HTML Converter
    Sử dụng các công cụ chuyên nghiệp có sẵn
    """
    
    def __init__(self, url: str, output_dir: str = "vandamtour_professional"):
        self.url = url
        self.output_dir = Path(output_dir)
        self.methods = []
        
        logger.info(f"Professional WordPress Converter initialized")
        logger.info(f"Target URL: {self.url}")
        logger.info(f"Output directory: {self.output_dir}")
    
    def check_dependencies(self) -> dict:
        """Kiểm tra các dependencies có sẵn"""
        deps = {
            'pywebcopy': False,
            'httrack': False,
            'wget': False,
            'curl': False,
            'git': False,
            'pip': False
        }
        
        # Check PyWebCopy
        try:
            import pywebcopy
            deps['pywebcopy'] = True
            logger.info("✓ PyWebCopy found")
        except ImportError:
            logger.warning("✗ PyWebCopy not found")
        
        # Check HTTrack
        try:
            result = subprocess.run(['httrack', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                deps['httrack'] = True
                logger.info("✓ HTTrack found")
        except:
            logger.warning("✗ HTTrack not found")
        
        # Check wget
        try:
            result = subprocess.run(['wget', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                deps['wget'] = True
                logger.info("✓ wget found")
        except:
            logger.warning("✗ wget not found")
        
        # Check curl
        try:
            result = subprocess.run(['curl', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                deps['curl'] = True
                logger.info("✓ curl found")
        except:
            logger.warning("✗ curl not found")
        
        # Check git
        try:
            result = subprocess.run(['git', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                deps['git'] = True
                logger.info("✓ git found")
        except:
            logger.warning("✗ git not found")
        
        # Check pip
        try:
            result = subprocess.run(['pip', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                deps['pip'] = True
                logger.info("✓ pip found")
        except:
            logger.warning("✗ pip not found")
        
        return deps
    
    def install_pywebcopy(self) -> bool:
        """Cài đặt PyWebCopy"""
        logger.info("Installing PyWebCopy...")
        
        try:
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', 'pywebcopy'
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                logger.info("✓ PyWebCopy installed successfully")
                return True
            else:
                logger.error(f"✗ Failed to install PyWebCopy: {result.stderr}")
                return False
        except Exception as e:
            logger.error(f"✗ Error installing PyWebCopy: {e}")
            return False
    
    def method_pywebcopy(self) -> bool:
        """Method 1: Sử dụng PyWebCopy"""
        logger.info("🔧 METHOD 1: Using PyWebCopy...")
        
        try:
            import pywebcopy
            
            output_path = self.output_dir / "pywebcopy_output"
            output_path.mkdir(parents=True, exist_ok=True)
            
            # Configure PyWebCopy
            kwargs = {
                'project_folder': str(output_path),
                'project_name': 'vandamtour',
                'bypass_robots': True,
                'debug': True,
                'delay': 0.5,
                'threaded': True
            }
            
            # Save complete webpage
            pywebcopy.save_webpage(
                url=self.url,
                **kwargs
            )
            
            logger.info("✅ PyWebCopy method completed successfully")
            self.methods.append("PyWebCopy")
            return True
            
        except Exception as e:
            logger.error(f"❌ PyWebCopy method failed: {e}")
            return False
    
    def method_httrack(self) -> bool:
        """Method 2: Sử dụng HTTrack"""
        logger.info("🔧 METHOD 2: Using HTTrack...")
        
        try:
            output_path = self.output_dir / "httrack_output"
            output_path.mkdir(parents=True, exist_ok=True)
            
            # HTTrack command
            cmd = [
                'httrack',
                self.url,
                '-O', str(output_path),
                '-n',  # No external links
                '-r6', # Recursion depth
                '-s0', # No robots.txt
                '-A999999999',  # Accept all file sizes
                '-%v', # Verbose
                '-F', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]
            
            logger.info(f"Running HTTrack: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info("✅ HTTrack method completed successfully")
                self.methods.append("HTTrack")
                return True
            else:
                logger.error(f"❌ HTTrack failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ HTTrack method failed: {e}")
            return False
    
    def method_wget(self) -> bool:
        """Method 3: Sử dụng wget"""
        logger.info("🔧 METHOD 3: Using wget...")
        
        try:
            output_path = self.output_dir / "wget_output"
            output_path.mkdir(parents=True, exist_ok=True)
            
            # wget command
            cmd = [
                'wget',
                '--recursive',
                '--page-requisites',
                '--html-extension',
                '--convert-links',
                '--restrict-file-names=windows',
                '--domains', self.url.split('//')[1].split('/')[0],
                '--no-parent',
                '--directory-prefix', str(output_path),
                '--user-agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                self.url
            ]
            
            logger.info(f"Running wget: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info("✅ wget method completed successfully")
                self.methods.append("wget")
                return True
            else:
                logger.error(f"❌ wget failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ wget method failed: {e}")
            return False
    
    def clone_simply_static_repo(self) -> bool:
        """Method 4: Clone Simply Static repo for reference"""
        logger.info("🔧 METHOD 4: Cloning Simply Static repo...")
        
        try:
            output_path = self.output_dir / "simply_static_repo"
            
            # Clone Simply Static repository
            cmd = [
                'git', 'clone',
                'https://github.com/Simply-Static/simply-static.git',
                str(output_path)
            ]
            
            logger.info(f"Cloning Simply Static repo...")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                logger.info("✅ Simply Static repo cloned successfully")
                self.methods.append("Simply Static Repo")
                return True
            else:
                logger.error(f"❌ Git clone failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Simply Static clone failed: {e}")
            return False
    
    def create_structured_output(self) -> bool:
        """Tạo structured output từ các methods"""
        logger.info("🔧 Creating structured output...")
        
        try:
            structured_path = self.output_dir / "structured"
            structured_path.mkdir(parents=True, exist_ok=True)
            
            # Create directory structure
            directories = [
                structured_path / "assets" / "css",
                structured_path / "assets" / "js", 
                structured_path / "assets" / "images",
                structured_path / "assets" / "fonts",
                structured_path / "components",
                structured_path / "pages",
                structured_path / "docs"
            ]
            
            for directory in directories:
                directory.mkdir(parents=True, exist_ok=True)
            
            # Find best output from methods
            best_output = None
            
            # Priority: PyWebCopy > HTTrack > wget
            for method_name in ["pywebcopy_output", "httrack_output", "wget_output"]:
                method_path = self.output_dir / method_name
                if method_path.exists():
                    best_output = method_path
                    break
            
            if best_output:
                logger.info(f"Using output from: {best_output.name}")
                
                # Copy and organize files
                self._organize_files(best_output, structured_path)
                
                # Create documentation
                self._create_documentation(structured_path)
                
                logger.info("✅ Structured output created successfully")
                return True
            else:
                logger.warning("❌ No valid output found to structure")
                return False
                
        except Exception as e:
            logger.error(f"❌ Structured output creation failed: {e}")
            return False
    
    def _organize_files(self, source_path: Path, target_path: Path):
        """Tổ chức files theo cấu trúc chuyên nghiệp"""
        import shutil
        
        logger.info("Organizing files...")
        
        # Find HTML files
        html_files = list(source_path.rglob("*.html"))
        if html_files:
            main_html = html_files[0]  # Take first HTML file as main
            shutil.copy2(main_html, target_path / "index.html")
            logger.info(f"✓ Copied main HTML: {main_html.name}")
        
        # Find and organize CSS files
        css_files = list(source_path.rglob("*.css"))
        for css_file in css_files:
            target_css = target_path / "assets" / "css" / css_file.name
            shutil.copy2(css_file, target_css)
            logger.info(f"✓ Copied CSS: {css_file.name}")
        
        # Find and organize JS files
        js_files = list(source_path.rglob("*.js"))
        for js_file in js_files:
            target_js = target_path / "assets" / "js" / js_file.name
            shutil.copy2(js_file, target_js)
            logger.info(f"✓ Copied JS: {js_file.name}")
        
        # Find and organize image files
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.ico']
        for ext in image_extensions:
            image_files = list(source_path.rglob(f"*{ext}"))
            for image_file in image_files:
                target_image = target_path / "assets" / "images" / image_file.name
                shutil.copy2(image_file, target_image)
                logger.info(f"✓ Copied image: {image_file.name}")
    
    def _create_documentation(self, structured_path: Path):
        """Tạo documentation"""
        docs_path = structured_path / "docs"
        
        readme_content = f"""# VẠN DẶM TOUR - Professional WordPress to HTML Conversion

## 🎯 Conversion Methods Used

{chr(10).join(f'- ✅ {method}' for method in self.methods)}

## 📁 Project Structure

```
{structured_path.name}/
├── index.html              # Main HTML file
├── assets/                 # All assets organized
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript files
│   ├── images/            # Images
│   └── fonts/             # Font files
├── components/            # Reusable components
├── pages/                 # Additional pages
└── docs/                  # Documentation
    └── README.md          # This file
```

## 🛠️ Tools Used

- **PyWebCopy**: Python library for complete webpage archiving
- **HTTrack**: Professional website copier
- **wget**: GNU recursive downloader
- **Simply Static**: WordPress plugin reference

## 📊 Conversion Results

- **Source URL**: {self.url}
- **Conversion Date**: {time.strftime('%Y-%m-%d %H:%M:%S')}
- **Methods Successful**: {len(self.methods)}
- **Output Structure**: Professional organized

## 🚀 Usage

1. Open `index.html` in a web browser
2. All assets are properly linked with relative paths
3. Website works offline with full functionality

---

**Generated by**: Professional WordPress to HTML Converter v1.0.0
"""
        
        with open(docs_path / "README.md", 'w', encoding='utf-8') as f:
            f.write(readme_content)
    
    def run_conversion(self) -> bool:
        """Chạy toàn bộ quá trình conversion"""
        logger.info("🚀 Starting Professional WordPress to HTML Conversion...")
        
        start_time = time.time()
        
        # Check dependencies
        deps = self.check_dependencies()
        
        # Install PyWebCopy if needed
        if not deps['pywebcopy'] and deps['pip']:
            self.install_pywebcopy()
            deps['pywebcopy'] = True
        
        # Create output directory
        self.output_dir.mkdir(exist_ok=True)
        
        # Try all available methods
        if deps['pywebcopy']:
            self.method_pywebcopy()
        
        if deps['httrack']:
            self.method_httrack()
        
        if deps['wget']:
            self.method_wget()
        
        if deps['git']:
            self.clone_simply_static_repo()
        
        # Create structured output
        self.create_structured_output()
        
        # Summary
        end_time = time.time()
        duration = end_time - start_time
        
        self.print_summary(duration)
        
        return len(self.methods) > 0
    
    def print_summary(self, duration: float):
        """In tóm tắt kết quả"""
        print("\n" + "="*60)
        print("🎉 PROFESSIONAL WORDPRESS TO HTML CONVERSION COMPLETED!")
        print("="*60)
        
        print(f"🎯 Target URL: {self.url}")
        print(f"📁 Output directory: {self.output_dir.absolute()}")
        print(f"⏱️  Duration: {duration:.2f} seconds")
        
        print(f"\n✅ Successful methods: {len(self.methods)}")
        for i, method in enumerate(self.methods, 1):
            print(f"   {i}. {method}")
        
        if self.methods:
            print(f"\n📁 Generated outputs:")
            for method in self.methods:
                method_dir = method.lower().replace(' ', '_').replace('repo', 'output')
                output_path = self.output_dir / f"{method_dir}"
                if output_path.exists():
                    print(f"   - {output_path}")
            
            structured_path = self.output_dir / "structured"
            if structured_path.exists():
                print(f"   - {structured_path} (⭐ RECOMMENDED)")
            
            print(f"\n🌐 Usage:")
            print(f"   Open: {structured_path / 'index.html'}")
            print(f"   Docs: {structured_path / 'docs' / 'README.md'}")
        
        else:
            print(f"\n❌ No methods succeeded!")
            print(f"   Please install required dependencies:")
            print(f"   - pip install pywebcopy")
            print(f"   - Install HTTrack: https://www.httrack.com/")
            print(f"   - Install wget: https://www.gnu.org/software/wget/")
        
        print("\n" + "="*60)

def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python professional_wp_to_html.py <URL>")
        print("Example: python professional_wp_to_html.py https://vandamtour.vn")
        sys.exit(1)
    
    url = sys.argv[1]
    
    # Create converter and run
    converter = ProfessionalWPConverter(url)
    success = converter.run_conversion()
    
    if success:
        print("\n🎉 Professional conversion successful!")
    else:
        print("\n❌ Professional conversion failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
